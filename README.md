# Telegram Bot with API Integration

A Telegram bot built with pyTelegramBotAPI that integrates with various web APIs.

## Features

### Core Features
- **Like System**: Send like requests for specific user IDs
- **Spam Detection**: Send spam requests for user IDs
- **Events Information**: Fetch current events data
- **System Information**: Get general system information
- **Status Checking**: Check status for specific user IDs

### Management Features
- **Bot Token Management**: Add and remove bot tokens with names
- **Group Access Control**: Manage which groups can use the bot
- **Admin Panel**: View all stored tokens and allowed groups
- **Permission System**: Admin-only commands and group restrictions
- **Database Storage**: SQLite database for persistent data storage

## Setup Instructions

### 1. Create a Telegram Bot

1. Open Telegram and search for `@BotFather`
2. Start a chat with Bo<PERSON>Father and send `/newbot`
3. Follow the instructions to create your bot
4. Copy the bot token provided by BotFather

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configure the Bot

1. Edit `config.py` and replace `YOUR_BOT_TOKEN_HERE` with your actual bot token from BotFather
2. Add your Telegram user ID to the `ADMIN_IDS` list in `config.py` to access admin commands

### 4. Run the Bot

```bash
python telegram_bot.py
```

## Available Commands

### Basic Commands
- `/start` - Welcome message and command list
- `/help` - Show detailed help information
- `/like <uid>` - Send like request for user ID
- `/spam <uid>` - Send spam request for user ID
- `/events` - Get events information
- `/info` - Get system information
- `/check <uid>` - Check status for user ID

### Admin Commands (Admins Only)
- `/TOKAN <name> <token>` - Add new bot token
- `/remove_tokan <name>` - Remove bot token
- `/add <group_id>` - Add allowed group
- `/remove <group_id>` - Remove allowed group
- `/panel` - Show bot management panel

## Usage Examples

### Basic Commands
```
/like 123456789
/spam 987654321
/events
/info
/check 555666777
```

### Admin Commands
```
/TOKAN mybot 123456789:ABCdefGHIjklMNOpqrSTUvwxYZ
/remove_tokan mybot
/add -1001234567890
/remove -1001234567890
/panel
```

## API Endpoints

The bot integrates with the following APIs:

- **Likes**: `https://likes-ch9ayfa-free.vercel.app/like`
- **Spam**: `https://spam-ch9ayfa.vercel.app/spam`
- **Events**: `https://eventes-ch9ayfa.vercel.app/eventes`
- **Info**: `https://info-ch9ayfa.vercel.app/2511293320`
- **Status Check**: `https://ch9ayfa-check-1.vercel.app/check_status`

## Error Handling

The bot includes comprehensive error handling for:
- Invalid user input
- Network request failures
- API response errors
- Unexpected exceptions

## Logging

The bot logs important events and errors to help with debugging and monitoring.

## Security Notes

- Keep your bot token secure and never share it publicly
- Consider implementing rate limiting for production use
- Validate all user inputs before processing
- Monitor API usage to prevent abuse

## Customization

You can easily customize the bot by:
- Modifying the API endpoints in `config.py`
- Adding new commands in `telegram_bot.py`
- Changing response messages and formatting
- Adding authentication or user restrictions
