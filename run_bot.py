#!/usr/bin/env python3
"""
Simple launcher script for the Telegram bot
"""

import os
import sys
from telegram_bot import bot, logger

def check_requirements():
    """Check if all requirements are met"""
    try:
        import telebot
        import requests
        print("✅ All dependencies are installed")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_config():
    """Check if bot is properly configured"""
    try:
        from config import BOT_TOKEN
        if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
            print("❌ Bot token not configured!")
            print("Please edit config.py and set your bot token from @BotFather")
            return False
        print("✅ Bot token is configured")
        return True
    except ImportError:
        print("❌ Config file not found!")
        return False

def main():
    """Main function to run the bot"""
    print("🤖 Telegram Bot Launcher")
    print("=" * 30)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check configuration
    if not check_config():
        sys.exit(1)
    
    print("🚀 Starting bot...")
    print("Press Ctrl+C to stop the bot")
    print("=" * 30)
    
    try:
        bot.infinity_polling()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        print(f"❌ Bot error: {e}")

if __name__ == "__main__":
    main()
