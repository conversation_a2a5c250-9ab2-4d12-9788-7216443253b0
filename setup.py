#!/usr/bin/env python3
"""
Setup script for the Telegram bot
"""

import os
import sys

def get_user_input(prompt, default=None):
    """Get user input with optional default value"""
    if default:
        user_input = input(f"{prompt} [{default}]: ").strip()
        return user_input if user_input else default
    else:
        while True:
            user_input = input(f"{prompt}: ").strip()
            if user_input:
                return user_input
            print("This field is required!")

def setup_config():
    """Setup configuration file"""
    print("🔧 إعداد البوت")
    print("=" * 30)
    
    # Get bot token
    bot_token = get_user_input("أدخل توكن البوت من @BotFather")
    
    # Get admin ID
    admin_id = get_user_input("أدخل معرف المشرف (Telegram User ID)")
    
    try:
        admin_id = int(admin_id)
    except ValueError:
        print("❌ معرف المشرف يجب أن يكون رقماً")
        return False
    
    # Read current config
    with open('config.py', 'r', encoding='utf-8') as f:
        config_content = f.read()
    
    # Replace values
    config_content = config_content.replace('YOUR_BOT_TOKEN_HERE', bot_token)
    config_content = config_content.replace('[123456789]', f'[{admin_id}]')
    
    # Write updated config
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ تم إعداد الملف بنجاح!")
    return True

def check_dependencies():
    """Check if dependencies are installed"""
    print("\n🔍 فحص التبعيات...")
    
    try:
        import telebot
        import requests
        print("✅ جميع التبعيات مثبتة")
        return True
    except ImportError as e:
        print(f"❌ تبعية مفقودة: {e}")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return False

def main():
    """Main setup function"""
    print("🤖 معالج إعداد البوت")
    print("=" * 40)
    
    # Check if config exists
    if not os.path.exists('config.py'):
        print("❌ ملف config.py غير موجود!")
        return
    
    # Setup configuration
    if not setup_config():
        return
    
    # Check dependencies
    if not check_dependencies():
        return
    
    print("\n🎉 تم الإعداد بنجاح!")
    print("يمكنك الآن تشغيل البوت باستخدام:")
    print("python run_bot.py")

if __name__ == "__main__":
    main()
