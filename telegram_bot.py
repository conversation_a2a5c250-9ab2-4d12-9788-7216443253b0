import telebot
import requests
import json
from typing import Optional
import logging
import os
from config import BOT_TOKEN, API_ENDPOINTS, REQUEST_TIMEOUT, LOG_LEVEL, LOG_FORMAT

# Configure logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# Bot configuration
if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
    print("❌ Please set your bot token in config.py")
    exit(1)

bot = telebot.TeleBot(BOT_TOKEN)

# API endpoints from config
LIKE_URL = API_ENDPOINTS["like"]
SPAM_URL = API_ENDPOINTS["spam"]
EVENTS_URL = API_ENDPOINTS["events"]
INFO_URL = API_ENDPOINTS["info"]
CHECK_STATUS_URL = API_ENDPOINTS["check_status"]

def make_request(url: str) -> Optional[dict]:
    """Make HTTP request and return JSON response"""
    try:
        response = requests.get(url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.json() if response.content else {"status": "success"}
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {e}")
        return None
    except json.JSONDecodeError:
        return {"status": "success", "message": "Request completed"}

@bot.message_handler(commands=['start'])
def send_welcome(message):
    """Welcome message with available commands"""
    welcome_text = """
🤖 Welcome to the Bot!

Available commands:
/like <uid> - Send like request
/spam <uid> - Send spam request  
/events - Get events information
/info - Get general information
/check <uid> - Check status
/help - Show this help message
    """
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def send_help(message):
    """Show help message"""
    help_text = """
📋 Commands Help:

/like <uid> - Send a like request for the specified user ID
Example: /like 123456789

/spam <uid> - Send a spam request for the specified user ID  
Example: /spam 123456789

/events - Get current events information

/info - Get general system information

/check <uid> - Check status for the specified user ID
Example: /check 123456789

Replace <uid> with actual user ID numbers.
    """
    bot.reply_to(message, help_text)

@bot.message_handler(commands=['like'])
def handle_like(message):
    """Handle like command"""
    try:
        # Extract UID from command
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Usage: /like <uid>\nExample: /like 123456789")
            return
        
        uid = parts[1]
        if not uid.isdigit():
            bot.reply_to(message, "❌ UID must be a number")
            return
        
        # Send like request
        bot.reply_to(message, "⏳ Sending like request...")
        url = LIKE_URL.format(uid=uid)
        result = make_request(url)
        
        if result:
            bot.reply_to(message, f"✅ Like request sent successfully for UID: {uid}")
        else:
            bot.reply_to(message, "❌ Failed to send like request")
            
    except Exception as e:
        logger.error(f"Error in handle_like: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['spam'])
def handle_spam(message):
    """Handle spam command"""
    try:
        # Extract UID from command
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Usage: /spam <uid>\nExample: /spam 123456789")
            return
        
        uid = parts[1]
        if not uid.isdigit():
            bot.reply_to(message, "❌ UID must be a number")
            return
        
        # Send spam request
        bot.reply_to(message, "⏳ Sending spam request...")
        url = SPAM_URL.format(uid=uid)
        result = make_request(url)
        
        if result:
            bot.reply_to(message, f"✅ Spam request sent successfully for UID: {uid}")
        else:
            bot.reply_to(message, "❌ Failed to send spam request")
            
    except Exception as e:
        logger.error(f"Error in handle_spam: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['events'])
def handle_events(message):
    """Handle events command"""
    try:
        bot.reply_to(message, "⏳ Fetching events information...")
        result = make_request(EVENTS_URL)
        
        if result:
            # Format the response nicely
            if isinstance(result, dict):
                response = "📅 Events Information:\n\n"
                for key, value in result.items():
                    response += f"• {key}: {value}\n"
            else:
                response = f"📅 Events: {result}"
            bot.reply_to(message, response)
        else:
            bot.reply_to(message, "❌ Failed to fetch events information")
            
    except Exception as e:
        logger.error(f"Error in handle_events: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['info'])
def handle_info(message):
    """Handle info command"""
    try:
        bot.reply_to(message, "⏳ Fetching information...")
        result = make_request(INFO_URL)
        
        if result:
            # Format the response nicely
            if isinstance(result, dict):
                response = "ℹ️ System Information:\n\n"
                for key, value in result.items():
                    response += f"• {key}: {value}\n"
            else:
                response = f"ℹ️ Info: {result}"
            bot.reply_to(message, response)
        else:
            bot.reply_to(message, "❌ Failed to fetch information")
            
    except Exception as e:
        logger.error(f"Error in handle_info: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['check'])
def handle_check(message):
    """Handle check status command"""
    try:
        # Extract UID from command
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Usage: /check <uid>\nExample: /check 123456789")
            return
        
        uid = parts[1]
        if not uid.isdigit():
            bot.reply_to(message, "❌ UID must be a number")
            return
        
        # Check status
        bot.reply_to(message, "⏳ Checking status...")
        url = CHECK_STATUS_URL.format(uid=uid)
        result = make_request(url)
        
        if result:
            # Format the response nicely
            if isinstance(result, dict):
                response = f"🔍 Status for UID {uid}:\n\n"
                for key, value in result.items():
                    response += f"• {key}: {value}\n"
            else:
                response = f"🔍 Status for UID {uid}: {result}"
            bot.reply_to(message, response)
        else:
            bot.reply_to(message, f"❌ Failed to check status for UID: {uid}")
            
    except Exception as e:
        logger.error(f"Error in handle_check: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(func=lambda message: True)
def handle_unknown(message):
    """Handle unknown commands"""
    bot.reply_to(message, "❓ Unknown command. Type /help to see available commands.")

if __name__ == "__main__":
    print("🤖 Bot is starting...")
    print("Press Ctrl+C to stop the bot")
    try:
        bot.infinity_polling()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        print(f"❌ Bot error: {e}")
