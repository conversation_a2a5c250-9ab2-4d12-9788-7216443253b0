import telebot
import requests
import json
from typing import Optional, Dict, List
import logging
import os
import sqlite3
from datetime import datetime
from config import BOT_TOKEN, API_ENDPOINTS, REQUEST_TIMEOUT, LOG_LEVEL, LOG_FORMAT, ADMIN_IDS

# Configure logging
logging.basicConfig(level=getattr(logging, LOG_LEVEL), format=LOG_FORMAT)
logger = logging.getLogger(__name__)

# Bot configuration
if BOT_TOKEN == "YOUR_BOT_TOKEN_HERE":
    print("❌ Please set your bot token in config.py")
    exit(1)

bot = telebot.TeleBot(BOT_TOKEN)

# Database setup
def init_database():
    """Initialize SQLite database for bot management"""
    conn = sqlite3.connect('bot_data.db')
    cursor = conn.cursor()

    # Create tokens table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS bot_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            token_name TEXT UNIQUE NOT NULL,
            token_value TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER NOT NULL
        )
    ''')

    # Create allowed groups table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS allowed_groups (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            group_id TEXT UNIQUE NOT NULL,
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            added_by INTEGER NOT NULL
        )
    ''')

    conn.commit()
    conn.close()

# Initialize database on startup
init_database()

# API endpoints from config
LIKE_URL = API_ENDPOINTS["like"]
SPAM_URL = API_ENDPOINTS["spam"]
EVENTS_URL = API_ENDPOINTS["events"]
INFO_URL = API_ENDPOINTS["info"]
CHECK_STATUS_URL = API_ENDPOINTS["check_status"]

# Admin user IDs are imported from config

# Database helper functions
def add_bot_token(token_name: str, token_value: str, user_id: int) -> bool:
    """Add a new bot token to database"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO bot_tokens (token_name, token_value, created_by) VALUES (?, ?, ?)',
            (token_name, token_value, user_id)
        )
        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError:
        return False
    except Exception as e:
        logger.error(f"Error adding token: {e}")
        return False

def remove_bot_token(token_name: str) -> bool:
    """Remove a bot token from database"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute('DELETE FROM bot_tokens WHERE token_name = ?', (token_name,))
        rows_affected = cursor.rowcount
        conn.commit()
        conn.close()
        return rows_affected > 0
    except Exception as e:
        logger.error(f"Error removing token: {e}")
        return False

def add_allowed_group(group_id: str, user_id: int) -> bool:
    """Add a group to allowed groups"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute(
            'INSERT INTO allowed_groups (group_id, added_by) VALUES (?, ?)',
            (group_id, user_id)
        )
        conn.commit()
        conn.close()
        return True
    except sqlite3.IntegrityError:
        return False
    except Exception as e:
        logger.error(f"Error adding group: {e}")
        return False

def remove_allowed_group(group_id: str) -> bool:
    """Remove a group from allowed groups"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute('DELETE FROM allowed_groups WHERE group_id = ?', (group_id,))
        rows_affected = cursor.rowcount
        conn.commit()
        conn.close()
        return rows_affected > 0
    except Exception as e:
        logger.error(f"Error removing group: {e}")
        return False

def get_all_tokens() -> List[tuple]:
    """Get all bot tokens"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute('SELECT token_name, token_value, created_at FROM bot_tokens')
        tokens = cursor.fetchall()
        conn.close()
        return tokens
    except Exception as e:
        logger.error(f"Error getting tokens: {e}")
        return []

def get_all_groups() -> List[tuple]:
    """Get all allowed groups"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute('SELECT group_id, added_at FROM allowed_groups')
        groups = cursor.fetchall()
        conn.close()
        return groups
    except Exception as e:
        logger.error(f"Error getting groups: {e}")
        return []

def is_admin(user_id: int) -> bool:
    """Check if user is admin"""
    return user_id in ADMIN_IDS

def is_group_allowed(group_id: str) -> bool:
    """Check if group is allowed"""
    try:
        conn = sqlite3.connect('bot_data.db')
        cursor = conn.cursor()
        cursor.execute('SELECT 1 FROM allowed_groups WHERE group_id = ?', (group_id,))
        result = cursor.fetchone()
        conn.close()
        return result is not None
    except Exception as e:
        logger.error(f"Error checking group: {e}")
        return False

def make_request(url: str) -> Optional[dict]:
    """Make HTTP request and return JSON response"""
    try:
        response = requests.get(url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        return response.json() if response.content else {"status": "success"}
    except requests.exceptions.RequestException as e:
        logger.error(f"Request failed: {e}")
        return None
    except json.JSONDecodeError:
        return {"status": "success", "message": "Request completed"}

@bot.message_handler(commands=['start'])
def send_welcome(message):
    """Welcome message with available commands"""
    welcome_text = """
╔══════════════════════════════╗
║        🤖 مرحباً بك في البوت        ║
╚══════════════════════════════╝

┌─────────── 📋 الأوامر المتاحة ───────────┐
│                                                                    │
│ 🔹 /like <uid> - إرسال طلب إعجاب                │
│ 🔹 /spam <uid> - إرسال طلب سبام              │
│ 🔹 /events - معلومات الأحداث                    │
│ 🔹 /info - معلومات النظام                        │
│ 🔹 /check <uid> - فحص الحالة                   │
│ 🔹 /panel - لوحة التحكم (مشرفين)              │
│ 🔹 /help - المساعدة التفصيلية                   │
│                                                                    │
└──────────────────────────────────────┘

💡 استخدم /help للحصول على شرح مفصل
    """
    bot.reply_to(message, welcome_text)

@bot.message_handler(commands=['help'])
def send_help(message):
    """Show help message"""
    help_text = """
╔═══════════════════════════════════╗
║           📋 دليل الأوامر المفصل           ║
╚═══════════════════════════════════╝

┌─────────── 🔧 الأوامر الأساسية ───────────┐
│                                                                        │
│ 💙 /like <uid>                                                │
│    ↳ إرسال طلب إعجاب للمعرف المحدد                    │
│    📝 مثال: /like 123456789                           │
│                                                                        │
│ 🚫 /spam <uid>                                               │
│    ↳ إرسال طلب سبام للمعرف المحدد                     │
│    📝 مثال: /spam 123456789                          │
│                                                                        │
│ 📅 /events                                                      │
│    ↳ الحصول على معلومات الأحداث الحالية              │
│                                                                        │
│ ℹ️ /info                                                          │
│    ↳ الحصول على معلومات النظام العامة                │
│                                                                        │
│ 🔍 /check <uid>                                               │
│    ↳ فحص حالة المعرف المحدد                              │
│    📝 مثال: /check 123456789                         │
│                                                                        │
└────────────────────────────────────────────┘

┌─────────── 👑 أوامر الإدارة ───────────┐
│                                                                        │
│ 🔑 /TOKAN <name> <token>                              │
│    ↳ إضافة توكن بوت جديد                                  │
│    📝 مثال: /TOKAN mybot 123:ABC                   │
│                                                                        │
│ ➕ /add <group_id>                                          │
│    ↳ إضافة مجموعة مسموحة                                 │
│    📝 مثال: /add -1001234567890                    │
│                                                                        │
│ ➖ /remove <group_id>                                     │
│    ↳ إزالة مجموعة                                              │
│    📝 مثال: /remove -1001234567890               │
│                                                                        │
│ 🗑️ /remove_tokan <name>                               │
│    ↳ إزالة توكن                                                 │
│    📝 مثال: /remove_tokan mybot                     │
│                                                                        │
│ 🎛️ /panel                                                        │
│    ↳ عرض لوحة التحكم                                        │
│                                                                        │
└────────────────────────────────────────────┘

⚠️ ملاحظة: استبدل <uid> بأرقام معرف المستخدم الفعلية
    """
    bot.reply_to(message, help_text)

# Admin command handlers
@bot.message_handler(commands=['TOKAN'])
def handle_add_token(message):
    """Handle adding new bot token"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ هذا الأمر متاح للمشرفين فقط")
        return

    try:
        parts = message.text.split(maxsplit=2)
        if len(parts) != 3:
            bot.reply_to(message, "❌ الاستخدام: /TOKAN <name> <token>\nمثال: /TOKAN abs123 456789:ABCdef")
            return

        _, token_name, token_value = parts

        if add_bot_token(token_name, token_value, message.from_user.id):
            success_msg = f"""
╔═══════════════════════════════════╗
║              ✅ نجح العملية              ║
╚═══════════════════════════════════╝

🔑 تم إضافة التوكن بنجاح!
📝 اسم التوكن: {token_name}
⏰ تم الحفظ في قاعدة البيانات

💡 يمكنك الآن استخدام /panel لعرض جميع التوكنات
            """
            bot.reply_to(message, success_msg)
            logger.info(f"Token added: {token_name} by user {message.from_user.id}")
        else:
            error_msg = f"""
╔═══════════════════════════════════╗
║              ❌ فشل العملية              ║
╚═══════════════════════════════════╝

🚫 فشل في إضافة التوكن
📝 اسم التوكن: {token_name}
⚠️ السبب المحتمل: الاسم موجود مسبقاً

💡 جرب اسماً مختلفاً أو استخدم /panel لعرض التوكنات الموجودة
            """
            bot.reply_to(message, error_msg)

    except Exception as e:
        logger.error(f"Error in handle_add_token: {e}")
        bot.reply_to(message, "❌ حدث خطأ أثناء معالجة طلبك")

@bot.message_handler(commands=['remove_tokan'])
def handle_remove_token(message):
    """Handle removing bot token"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ هذا الأمر متاح للمشرفين فقط")
        return

    try:
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ الاستخدام: /remove_tokan <name>\nمثال: /remove_tokan abs123")
            return

        token_name = parts[1]

        if remove_bot_token(token_name):
            bot.reply_to(message, f"✅ تم حذف التوكن بنجاح: {token_name}")
            logger.info(f"Token removed: {token_name} by user {message.from_user.id}")
        else:
            bot.reply_to(message, f"❌ لم يتم العثور على التوكن: {token_name}")

    except Exception as e:
        logger.error(f"Error in handle_remove_token: {e}")
        bot.reply_to(message, "❌ حدث خطأ أثناء معالجة طلبك")

@bot.message_handler(commands=['add'])
def handle_add_group(message):
    """Handle adding allowed group"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ هذا الأمر متاح للمشرفين فقط")
        return

    try:
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ الاستخدام: /add <group_id>\nمثال: /add -1001234567890")
            return

        group_id = parts[1]

        if add_allowed_group(group_id, message.from_user.id):
            bot.reply_to(message, f"✅ تم إضافة المجموعة بنجاح: {group_id}")
            logger.info(f"Group added: {group_id} by user {message.from_user.id}")
        else:
            bot.reply_to(message, f"❌ فشل في إضافة المجموعة. قد تكون موجودة مسبقاً: {group_id}")

    except Exception as e:
        logger.error(f"Error in handle_add_group: {e}")
        bot.reply_to(message, "❌ حدث خطأ أثناء معالجة طلبك")

@bot.message_handler(commands=['remove'])
def handle_remove_group(message):
    """Handle removing allowed group"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ هذا الأمر متاح للمشرفين فقط")
        return

    try:
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ الاستخدام: /remove <group_id>\nمثال: /remove -1001234567890")
            return

        group_id = parts[1]

        if remove_allowed_group(group_id):
            bot.reply_to(message, f"✅ تم حذف المجموعة بنجاح: {group_id}")
            logger.info(f"Group removed: {group_id} by user {message.from_user.id}")
        else:
            bot.reply_to(message, f"❌ لم يتم العثور على المجموعة: {group_id}")

    except Exception as e:
        logger.error(f"Error in handle_remove_group: {e}")
        bot.reply_to(message, "❌ حدث خطأ أثناء معالجة طلبك")

@bot.message_handler(commands=['panel'])
def handle_panel(message):
    """Handle bot panel command"""
    if not is_admin(message.from_user.id):
        bot.reply_to(message, "❌ هذا الأمر متاح للمشرفين فقط")
        return

    try:
        # Get all tokens and groups
        tokens = get_all_tokens()
        groups = get_all_groups()

        panel_text = """
╔═══════════════════════════════════╗
║           🎛️ لوحة التحكم الإدارية           ║
╚═══════════════════════════════════╝

"""

        # Display tokens
        panel_text += f"┌─────────── 🔑 التوكنات المحفوظة ({len(tokens)}) ───────────┐\n"
        if tokens:
            for i, (name, token, created_at) in enumerate(tokens, 1):
                # Hide most of the token for security
                hidden_token = token[:10] + "..." + token[-5:] if len(token) > 15 else token
                panel_text += f"│ {i:2d}. 📋 {name:<15} │ 🔐 {hidden_token:<20} │\n"
        else:
            panel_text += "│           🚫 لا توجد توكنات محفوظة           │\n"

        panel_text += "└─────────────────────────────────────────────┘\n\n"

        # Display groups
        panel_text += f"┌─────────── 👥 المجموعات المسموحة ({len(groups)}) ───────────┐\n"
        if groups:
            for i, (group_id, added_at) in enumerate(groups, 1):
                panel_text += f"│ {i:2d}. 🏷️ {group_id:<30} │\n"
        else:
            panel_text += "│           🚫 لا توجد مجموعات مسموحة           │\n"

        panel_text += "└─────────────────────────────────────────────┘\n\n"

        panel_text += """
┌─────────── 📊 إحصائيات سريعة ───────────┐
│ 🔢 إجمالي التوكنات: {tokens_count:<20} │
│ 🔢 إجمالي المجموعات: {groups_count:<19} │
│ 👑 المشرف النشط: نعم                      │
└─────────────────────────────────────────┘

💡 استخدم الأوامر الإدارية لإدارة البوت
        """.format(tokens_count=len(tokens), groups_count=len(groups))

        bot.reply_to(message, panel_text)

    except Exception as e:
        logger.error(f"Error in handle_panel: {e}")
        bot.reply_to(message, "❌ حدث خطأ أثناء معالجة طلبك")

def check_permissions(message) -> bool:
    """Check if user has permission to use the bot"""
    # Allow in private chats for admins
    if message.chat.type == 'private':
        return True

    # Check if group is allowed
    group_id = str(message.chat.id)
    return is_group_allowed(group_id)

@bot.message_handler(commands=['like'])
def handle_like(message):
    """Handle like command"""
    if not check_permissions(message):
        bot.reply_to(message, "❌ هذا البوت غير مسموح في هذه المجموعة")
        return

    try:
        # Extract UID from command
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Usage: /like <uid>\nExample: /like 123456789")
            return
        
        uid = parts[1]
        if not uid.isdigit():
            bot.reply_to(message, "❌ UID must be a number")
            return
        
        # Send like request
        processing_msg = """
┌─────────────────────────────────┐
│        ⏳ جاري المعالجة...        │
│     🔄 إرسال طلب الإعجاب...     │
└─────────────────────────────────┘
        """
        bot.reply_to(message, processing_msg)

        url = LIKE_URL.format(uid=uid)
        result = make_request(url)

        if result:
            success_msg = f"""
╔═══════════════════════════════════╗
║              ✅ تم بنجاح              ║
╚═══════════════════════════════════╝

💙 تم إرسال طلب الإعجاب بنجاح!
🆔 معرف المستخدم: {uid}
🌐 تم التواصل مع الخادم
⚡ الحالة: مكتمل

🎯 النتيجة: {result.get('status', 'نجح')}
            """
            bot.reply_to(message, success_msg)
        else:
            error_msg = f"""
╔═══════════════════════════════════╗
║              ❌ فشل العملية              ║
╚═══════════════════════════════════╝

💔 فشل في إرسال طلب الإعجاب
🆔 معرف المستخدم: {uid}
🌐 مشكلة في الاتصال بالخادم

💡 جرب مرة أخرى لاحقاً
            """
            bot.reply_to(message, error_msg)
            
    except Exception as e:
        logger.error(f"Error in handle_like: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['spam'])
def handle_spam(message):
    """Handle spam command"""
    if not check_permissions(message):
        bot.reply_to(message, "❌ هذا البوت غير مسموح في هذه المجموعة")
        return

    try:
        # Extract UID from command
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Usage: /spam <uid>\nExample: /spam 123456789")
            return
        
        uid = parts[1]
        if not uid.isdigit():
            bot.reply_to(message, "❌ UID must be a number")
            return
        
        # Send spam request
        processing_msg = """
┌─────────────────────────────────┐
│        ⏳ جاري المعالجة...        │
│      🚫 إرسال طلب السبام...      │
└─────────────────────────────────┘
        """
        bot.reply_to(message, processing_msg)

        url = SPAM_URL.format(uid=uid)
        result = make_request(url)

        if result:
            success_msg = f"""
╔═══════════════════════════════════╗
║              ✅ تم بنجاح              ║
╚═══════════════════════════════════╝

🚫 تم إرسال طلب السبام بنجاح!
🆔 معرف المستخدم: {uid}
🌐 تم التواصل مع الخادم
⚡ الحالة: مكتمل

🎯 النتيجة: {result.get('status', 'نجح')}
            """
            bot.reply_to(message, success_msg)
        else:
            error_msg = f"""
╔═══════════════════════════════════╗
║              ❌ فشل العملية              ║
╚═══════════════════════════════════╝

💥 فشل في إرسال طلب السبام
🆔 معرف المستخدم: {uid}
🌐 مشكلة في الاتصال بالخادم

💡 جرب مرة أخرى لاحقاً
            """
            bot.reply_to(message, error_msg)
            
    except Exception as e:
        logger.error(f"Error in handle_spam: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['events'])
def handle_events(message):
    """Handle events command"""
    if not check_permissions(message):
        bot.reply_to(message, "❌ هذا البوت غير مسموح في هذه المجموعة")
        return

    try:
        bot.reply_to(message, "⏳ Fetching events information...")
        result = make_request(EVENTS_URL)
        
        if result:
            # Format the response nicely
            if isinstance(result, dict):
                response = "📅 Events Information:\n\n"
                for key, value in result.items():
                    response += f"• {key}: {value}\n"
            else:
                response = f"📅 Events: {result}"
            bot.reply_to(message, response)
        else:
            bot.reply_to(message, "❌ Failed to fetch events information")
            
    except Exception as e:
        logger.error(f"Error in handle_events: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['info'])
def handle_info(message):
    """Handle info command"""
    if not check_permissions(message):
        bot.reply_to(message, "❌ هذا البوت غير مسموح في هذه المجموعة")
        return

    try:
        bot.reply_to(message, "⏳ Fetching information...")
        result = make_request(INFO_URL)
        
        if result:
            # Format the response nicely
            if isinstance(result, dict):
                response = "ℹ️ System Information:\n\n"
                for key, value in result.items():
                    response += f"• {key}: {value}\n"
            else:
                response = f"ℹ️ Info: {result}"
            bot.reply_to(message, response)
        else:
            bot.reply_to(message, "❌ Failed to fetch information")
            
    except Exception as e:
        logger.error(f"Error in handle_info: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(commands=['check'])
def handle_check(message):
    """Handle check status command"""
    if not check_permissions(message):
        bot.reply_to(message, "❌ هذا البوت غير مسموح في هذه المجموعة")
        return

    try:
        # Extract UID from command
        parts = message.text.split()
        if len(parts) != 2:
            bot.reply_to(message, "❌ Usage: /check <uid>\nExample: /check 123456789")
            return
        
        uid = parts[1]
        if not uid.isdigit():
            bot.reply_to(message, "❌ UID must be a number")
            return
        
        # Check status
        bot.reply_to(message, "⏳ Checking status...")
        url = CHECK_STATUS_URL.format(uid=uid)
        result = make_request(url)
        
        if result:
            # Format the response nicely
            if isinstance(result, dict):
                response = f"🔍 Status for UID {uid}:\n\n"
                for key, value in result.items():
                    response += f"• {key}: {value}\n"
            else:
                response = f"🔍 Status for UID {uid}: {result}"
            bot.reply_to(message, response)
        else:
            bot.reply_to(message, f"❌ Failed to check status for UID: {uid}")
            
    except Exception as e:
        logger.error(f"Error in handle_check: {e}")
        bot.reply_to(message, "❌ An error occurred while processing your request")

@bot.message_handler(func=lambda msg: True)
def handle_unknown(message):
    """Handle unknown commands"""
    if not check_permissions(message):
        return  # Silently ignore in unauthorized groups
    bot.reply_to(message, "❓ أمر غير معروف. اكتب /help لرؤية الأوامر المتاحة.")

if __name__ == "__main__":
    print("🤖 Bot is starting...")
    print("Press Ctrl+C to stop the bot")
    try:
        bot.infinity_polling()
    except KeyboardInterrupt:
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")
        print(f"❌ Bot error: {e}")
