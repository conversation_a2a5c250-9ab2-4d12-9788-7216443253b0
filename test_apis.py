#!/usr/bin/env python3
"""
Test script to verify API endpoints are working
"""

import requests
import json
from config import API_ENDPOINTS, REQUEST_TIMEOUT

def test_endpoint(name: str, url: str, test_uid: str = "8784017287"):
    """Test a single API endpoint"""
    print(f"\n🔍 Testing {name}...")
    print(f"URL: {url}")
    
    try:
        # Format URL with test UID if needed
        if "{uid}" in url:
            formatted_url = url.format(uid=test_uid)
        else:
            formatted_url = url
            
        print(f"Formatted URL: {formatted_url}")
        
        # Make request
        response = requests.get(formatted_url, timeout=REQUEST_TIMEOUT)
        print(f"Status Code: {response.status_code}")
        
        # Try to parse JSON response
        try:
            json_data = response.json()
            print(f"Response: {json.dumps(json_data, indent=2)}")
        except json.JSONDecodeError:
            print(f"Response (text): {response.text[:200]}...")
        
        if response.status_code == 200:
            print("✅ Endpoint is working")
        else:
            print("⚠️ Endpoint returned non-200 status")
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Test all API endpoints"""
    print("🧪 API Endpoints Test")
    print("=" * 40)
    
    test_uid = "8784017287"  # Default test UID
    
    # Test each endpoint
    for name, url in API_ENDPOINTS.items():
        test_endpoint(name, url, test_uid)
    
    print("\n" + "=" * 40)
    print("✅ Testing completed")

if __name__ == "__main__":
    main()
